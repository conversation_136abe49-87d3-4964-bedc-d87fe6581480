<template>
    <div class="new-seedling-monitoring" v-if="dialogVisible">

        <!-- 查询无结果提示框 -->
        <div class="no-video-dialog-wrapper">
            <el-dialog
                title="提示"
                :visible.sync="noVideoDialog.visible"
                width="374px"
                :show-close="false"
                :close-on-click-modal="true"
                :close-on-press-escape="false"
                :modal-append-to-body="false"
                center
                @close="closeNoVideoDialog"
            >
                <img src="../assets/image/centralControlPlatform/dialogLine.png" alt="" class="dialog-line">
                <div class="dialog-content">
                    <div class="no-video-icon">
                        <img src="../assets/image/centralControlPlatform/noVideo.png" alt="无视频" />
                    </div>
                    <p class="no-video-text">无视频</p>
                </div>
            </el-dialog>
        </div>

        <!-- 视频回放弹窗 -->
        <div class="seedling-detection-dialog">
            <el-dialog
                title="视频回放"
                :visible.sync="dialogVisible"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :modal="false"
                :modal-append-to-body="false"
                width="1256px"
                custom-class="seedling-detection-dialog"
            >
                <!-- 关闭按钮 -->
                <img src="../assets/image/eppoWisdom/close.png" alt="关闭" class="clone" @click="closeDialog" />
                <div class="wire"></div>

                <!-- 弹窗内容容器 -->
                <div class="content-container">
                    <!-- 第一行控制区域 -->
                    <div class="control-row">
                        <div class="control-left">
                            <el-select
                                v-model="selectedArea"
                                class="area-select formStyle"
                                placeholder="请选择区域"
                                popper-class="selectStyle_list"
                                :loading="loading"
                                @change="onAreaChange"
                            >
                                <el-option
                                    v-for="area in areaOptions"
                                    :key="area.value"
                                    :label="area.label"
                                    :value="area.value"
                                />
                            </el-select>
                            <el-select
                                v-model="selectedDevice"
                                class="device-select formStyle"
                                placeholder="请选择设备"
                                popper-class="selectStyle_list"
                                :loading="loading"
                                :disabled="!selectedArea"
                                @change="onDeviceChange"
                            >
                                <el-option
                                    v-for="device in deviceOptions"
                                    :key="device.value"
                                    :label="device.label"
                                    :value="device.value"
                                />
                            </el-select>
                            <el-date-picker
                                v-model="selectedDate"
                                class="date-select formStyle"
                                type="date"
                                placeholder="选择日期"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                
                            />
                            <div class="time-range-container formStyle">
                                <el-time-picker
                                    v-model="timeRange"
                                    class="time-range-picker formStyle"
                                    is-range
                                    range-separator="至"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    format="HH:mm:ss"
                                    value-format="HH:mm:ss"
                                    @change="onTimeRangeChange"
                                />
                            </div>
                            <button
                                class="query-btn"
                                @click="queryData"
                                :disabled="loading || !selectedArea || !selectedDevice || !selectedDate"
                            >
                                {{ loading ? '查询中...' : '查询' }}
                            </button>
                        </div>
                        <div class="control-right">
                            <button class="realtime-btn" @click="goToRealtime">实时</button>
                        </div>
                    </div>

                    <!-- 视频展示区域 -->
                    <div class="video-container">
                        <div class="video-border">
                            <div class="video-window">
                                <!-- 视频内容区域 -->
                                <div class="video-content" v-if="videoSrc">
                                    <video
                                        ref="videoElement"
                                        class="video-element"
                                        :src="videoSrc"
                                        controls
                                        preload="metadata"
                                        @error="handleVideoError"
                                    >
                                        您的浏览器不支持视频播放
                                    </video>

                                </div>
                                <div class="video-placeholder" v-else>
                                    <img src="../assets/image/monitoringCenter/videoPlay.png" alt="视频播放" class="video-placeholder-icon" />
                                    <div class="video-info">{{ getVideoDisplayText() }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService.js'
import CropCaseService from '../jaxrs/concrete/com.zny.ia.api.CropCaseService.js'

export default {
    name: 'NewSeedlingMonitoring',
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            // 弹窗数据
            videoSrc: '', // 视频源地址，待接口接入时使用
            dialogVisible: false,
            selectedArea: '',
            selectedDevice: '',
            selectedDate: '',
            timeRange: [], // 时间范围选择器的值 [开始时间, 结束时间]
            startTime: '',
            endTime: '',

            
            // 区域选项 - 从接口获取
            areaOptions: [],

            // 设备选项 - 从接口获取
            deviceOptions: [],

            // 无视频提示框
            noVideoDialog: {
                visible: false
            },

            // 加载状态
            loading: false

        }
    },
    watch: {
        visible(newVal) {
            this.dialogVisible = newVal
            if (newVal) {
                this.initializeData()
            }
        },
        dialogVisible(newVal) {
            this.$emit('update:visible', newVal)
        }
    },
    mounted() {
        // 获取区域列表
        this.getAreaList()
    },

    methods: {
        // 获取区域列表
        getAreaList() {
            this.loading = true
            CommonService.allAreaOfCurrentUserManage()
                .then(res => {
                    this.areaOptions = res.map(area => ({
                        value: area.areaId,
                        label: area.areaName
                    }))
                })
                .catch(err => {
                    console.error('获取区域列表失败:', err)
                    this.$message.error('获取区域列表失败')
                })
                .finally(() => {
                    this.loading = false
                })
        },

        // 根据区域ID获取设备列表
        getEquipmentList(areaId) {
            if (!areaId) {
                this.deviceOptions = []
                return
            }

            this.loading = true
            CommonService.equipmentListByAreaId(areaId)
                .then(res => {
                    this.deviceOptions = res.map(equipment => ({
                        value: equipment.equipmentId,
                        label: equipment.equipmentName
                    }))
                })
                .catch(err => {
                    console.error('获取设备列表失败:', err)
                    this.$message.error('获取设备列表失败')
                })
                .finally(() => {
                    this.loading = false
                })
        },

        // 打开弹窗
        openDialog() {
            this.dialogVisible = true
            this.initializeData()
        },
        
        // 关闭弹窗
        closeDialog() {
            this.dialogVisible = false
        },
        
        // 初始化数据
        initializeData() {
            // 设置默认值
            if (this.areaOptions.length > 0) {
                this.selectedArea = this.areaOptions[0].value
                // 获取该区域的设备列表
                this.getEquipmentList(this.selectedArea)
            }
            // 设置默认日期为今天
            this.selectedDate = this.getCurrentDate()
            this.timeRange = ['00:00:00', '23:59:59'] // 设置默认时间范围
            this.startTime = '00:00:00'
            this.endTime = '23:59:59'
        },
        
        // 跳转到实时监控
        goToRealtime() {
            console.log('跳转到实时监控')
            this.dialogVisible = false
            this.$emit('go-to-realtime')
        },
        
        // 区域选择改变
        onAreaChange(areaId) {
            console.log('区域选择改变:', areaId)
            this.selectedDevice = ''
            this.videoSrc = ''
            // 获取该区域的设备列表
            this.getEquipmentList(areaId)
        },

        // 设备选择改变
        onDeviceChange(equipmentId) {
            console.log('设备选择改变:', equipmentId)
        },
        
        
        // 时间范围选择改变
        onTimeRangeChange(value) {
            console.log('时间范围选择改变:', value)
            
            // 更新开始时间和结束时间
            if (value && Array.isArray(value) && value.length === 2) {
                this.startTime = value[0]
                this.endTime = value[1]
            } else {
                this.startTime = ''
                this.endTime = ''
            }
            
            this.loadVideoData()
        },
        
        // 查询数据
        queryData() {
            if (!this.selectedArea) {
                this.$message.warning('请选择区域')
                return
            }
            if (!this.selectedDevice) {
                this.$message.warning('请选择设备')
                return
            }
            if (!this.selectedDate) {
                this.$message.warning('请选择日期')
                return
            }
            if (!this.startTime || !this.endTime) {
                this.$message.warning('请选择时间范围')
                return
            }

            console.log('查询数据:', {
                area: this.selectedArea,
                device: this.selectedDevice,
                date: this.selectedDate,
                startTime: this.startTime,
                endTime: this.endTime
            })

            this.loadVideoData()
        },

        // 加载视频数据
        loadVideoData() {
            this.loading = true

            // 调用cropCaseMonitoring接口获取监控数据
            CropCaseService.cropCaseMonitoring(this.selectedDevice)
                .then(res => {
                    if (res && res.videoPlaybackLocalUrl) {
                        // 构建时间参数
                        const beginTime = this.formatDateTime(this.selectedDate, this.startTime)
                        const endTime = this.formatDateTime(this.selectedDate, this.endTime)

                        // 拼接时间参数到本地回放地址
                        this.videoSrc = `${res.videoPlaybackLocalUrl}?begin=${beginTime}&end=${endTime}`

                        console.log('获取到回放视频:', this.videoSrc)
                    } else {
                        this.videoSrc = ''
                        this.showNoVideoDialog()
                    }
                })
                .catch(err => {
                    console.error('获取视频数据失败:', err)
                    this.$message.error('获取视频数据失败')
                    this.videoSrc = ''
                    this.showNoVideoDialog()
                })
                .finally(() => {
                    this.loading = false
                })
        },

        // 格式化日期时间为接口需要的格式 (20250730000000)
        formatDateTime(date, time) {
            if (!date || !time) return ''

            // date格式: 2025-07-30
            // time格式: 00:00:00
            const dateStr = date.replace(/-/g, '') // 20250730
            const timeStr = time.replace(/:/g, '') // 000000

            return dateStr + timeStr // 20250730000000
        },

        // 显示无视频提示框
        showNoVideoDialog() {
            this.noVideoDialog.visible = true
        },

        // 关闭无视频提示框
        closeNoVideoDialog() {
            this.noVideoDialog.visible = false
        },
        
        // 获取当前日期
        getCurrentDate() {
            const now = new Date()
            return now.getFullYear() + '-' +
                   String(now.getMonth() + 1).padStart(2, '0') + '-' +
                   String(now.getDate()).padStart(2, '0')
        },


        
        // 获取视频显示文本
        getVideoDisplayText() {
            if (this.selectedArea && this.selectedDevice && this.selectedDate) {
                const areaLabel = this.areaOptions.find(area => area.value === this.selectedArea)?.label || this.selectedArea
                const deviceLabel = this.deviceOptions.find(device => device.value === this.selectedDevice)?.label || this.selectedDevice
                const timeInfo = `${this.startTime || '00:00:00'} - ${this.endTime || '23:59:59'}`
                return `${areaLabel} - ${deviceLabel} - ${this.selectedDate} - ${timeInfo}`
            }
            return '请选择查询条件'
        },
        
        // 视频错误处理
        handleVideoError() {
            console.error('苗情视频加载失败')
            this.videoSrc = ''
        }
    }
}
</script>

<style lang="less" scoped>
// 苗情监测显示区域样式
.new-seedling-monitoring {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

// 苗情检测弹窗样式
.seedling-detection-dialog ::v-deep .el-dialog {
    height: 854px!important;
    margin-top: calc(50vh - 400px) !important;

    .el-dialog__header {
        padding: 23px 30px 23px 30px;
        text-align: left;

        .el-dialog__title {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #DFEEF3;
            position: relative;
        }
    }

    .el-dialog__body {
        height: calc(100% - 114px);
        padding: 0 52px 30px 52px!important;
        position: relative;

        .clone {
            position: absolute;
            top: -55px!important;
            right: 24px!important;
            cursor: pointer;
            z-index: 10;
        }
        .wire{
            height: 2px;
            position: relative;
            top: -15px;
            background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
            margin-bottom: 20px;
        }

        .content-container {
            height: 100%;

            // 控制行样式
            .control-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24px;
                height: 32px;

                .control-left {
                    display: flex;
                    align-items: center;
                    gap: 20px;

                    .query-btn {
                        width: 80px;
                        height: 32px;
                        background: rgba(0,245,255,0);
                        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5);
                        border-radius: 2px;
                        border: 0;
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #DFEEF3;
                        cursor: pointer;

                        &:hover {
                            background: rgba(0,245,255,0.1);
                        }
                    }

                    .area-select, .device-select, .date-select {
                        width: 140px;
                        height: 32px;
                    }

                    .time-range-container {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        height: 32px;
                        width: 240px;
                        .time-range-picker {
                            width: 280px;
                            height: 32px;
                        }
                    }
                }

                .control-right {
                    .realtime-btn {
                        width: 80px;
                        height: 32px;
                        background: rgba(0,245,255,0);
                        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5);
                        border-radius: 2px;
                        border: 0;
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #DFEEF3;
                        cursor: pointer;

                        &:hover {
                            background: rgba(0,245,255,0.1);
                        }
                    }
                }
            }

            // 视频容器样式
            .video-container {
                width: 100%;
                display: flex;
                justify-content: center;

                .video-border {
                    width: 1152px;
                    height: 662px;
                    border: 1px solid rgba(0, 244, 253, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: rgba(0, 0, 0, 0.1);

                    .video-window {
                        width: 1120px;
                        height: 630px;
                        background: #000000;
                        border-radius: 2px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        position: relative;

                        .video-content {
                            width: 100%;
                            height: 100%;
                            position: relative;

                            .video-element {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }


                        }

                        .video-placeholder {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;

                            .video-placeholder-icon {
                                width: 64px;
                                height: 64px;
                                opacity: 0.5;
                                margin-bottom: 20px;
                            }

                            .video-info {
                                font-size: 16px;
                                font-family: Microsoft YaHei;
                                color: #DFEEF3;
                                opacity: 0.7;
                            }
                        }
                    }
                }
            }
        }
    }
}

// 自定义下拉选择框和日期选择器样式
.seedling-detection-dialog ::v-deep .formStyle {
    .el-input__inner {
        color: rgba(254, 255, 255, 0.6) !important;
        background: rgba(0,245,255,0) !important;
        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5) !important;
        border-radius: 2px !important;
        border: 0 !important;
    }

    .el-select .el-input .el-select__caret {
        color: rgba(254, 255, 255, 0.6) !important;
    }

    .el-date-editor .el-input__inner {
        color: rgba(254, 255, 255, 0.6) !important;
        background: rgba(0,245,255,0) !important;
        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5) !important;
        border-radius: 2px !important;
        border: 0 !important;
    }

    .el-date-editor .el-input__prefix {
        color: rgba(254, 255, 255, 0.6) !important;
    }
    .el-date-editor .el-range__close-icon {
       line-height: 27px !important;
    }

    .el-time-picker .el-input__inner {
        color: rgba(254, 255, 255, 0.6) !important;
        background: rgba(0,245,255,0) !important;
        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5) !important;
        border-radius: 2px !important;
        border: 0 !important;
    }

    .el-time-picker .el-input__icon {
        line-height: 27px !important;
        color: rgba(254, 255, 255, 0.6) !important;
    }

    .el-time-picker .el-range-separator {
        color: rgba(254, 255, 255, 0.6) !important;
        line-height: 27px !important;
    }

}

// 无视频提示框样式
.no-video-dialog-wrapper {
    :deep(.v-modal) {
        background: rgba(0, 0, 0, 0.5) !important;
    }

    :deep(.el-dialog) {
        width: 374px !important;
        height: 246px;
        background: #003D42;
        font-family: MicrosoftYaHei;
        margin-top: calc(50vh - 118px) !important;

        .el-dialog__header {
            height: 60px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            .el-dialog__title {
                color: #DFEEF3;
                font-size: 16px;
                font-weight: bold;
            }
        }

        .el-dialog__body {
            padding: 0;
            height: calc(100% - 60px);
            display: flex;
            flex-direction: column;
            color: #DFEEF3;

            .dialog-line {
                position: relative;
                top: -20px;
            }

            .dialog-content {
                // flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 15px;

                .no-video-icon {
                    margin-bottom: 20px;

                    img {
                        width: 64px;
                        height: 64px;
                        opacity: 0.8;
                    }
                }

                .no-video-text {
                    color: #DFEEF3;
                    font-size: 20px;
                    margin: 0;
                    text-align: center;
                    line-height: 1.5;
                }
            }
        }
    }
}
</style>
