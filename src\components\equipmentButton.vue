<template>
  <div class="equipmentButton">
    <div class="button" v-show="showWarning" @click="openAlarmSidebar">
      <div class="button_img">
        <img src="../assets/image/centralControlPlatform/icon1.png" alt="">
      </div>
      <div class="button_text">
        <div v-if="total.alarmNum==null">0</div>
        <div v-else>{{total.alarmNum}}</div>
        <div>报警设备</div>
      </div>
    </div>
    <div class="button" v-show="showTotal">
      <div class="button_img">
        <img src="../assets/image/centralControlPlatform/icon2.png" alt="">
      </div>
      <div class="button_text">
        <div v-if="total.totalNum==null">0</div>
        <div v-else>{{total.totalNum}}</div>
        <div>设备总数</div>
      </div>
    </div>
    <div class="button" v-show="showOnline">
      <div class="button_img">
        <img src="../assets/image/centralControlPlatform/icon3.png" alt="">
      </div>
      <div class="button_text">
        <div v-if="total.onlineNum==null">0</div>
        <div v-else>{{total.onlineNum}}</div>
        <div>在线设备</div>
      </div>
    </div>
  </div>
</template>
<script>
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService.js';
import EquipmentService from '../jaxrs/concrete/com.zny.ia.api.ConcreteService.EquipmentService.js';
export default {
  props:{
    showWarning: {
      type: Boolean,
      default: true,
    },
    showTotal: {
      type: Boolean,
      default: true,
    },
    showOnline: {
      type: Boolean,
      default: true,
    },
  },
  data(){
    return{
      total:{}
    }
  },
  mounted(){
    this.findEquipmentTotal()
  },
  methods:{
    findEquipmentTotal(){
      CommonService.findEquipmentTotal()
      .then(res=>{
        this.total=res
      })
    },
    // 打开报警设备侧边栏
    openAlarmSidebar(){
      // 获取报警设备列表
      this.getAlarmEquipmentList();
    },
    // 获取报警设备列表
    getAlarmEquipmentList(){
      const po = {
        num: 1,
        pageSize: 1000, // 获取所有报警设备
        condition: {
          areaId: null, // 不限制区域，获取全部
          equipmentTypeId: null, // 不限制设备类型
          alarmStatus: true, // 只获取报警设备
          online: null, // 不限制在线状态
        },
      };

      EquipmentService.eqSelectList(po).then(res => {
        console.log('报警设备列表:', res);
        // 发送事件，传递报警设备列表数据
        this.zEmit('openAlarmEquipmentSidebar', res.list);
      }).catch(err => {
        console.error('获取报警设备列表失败:', err);
        // 即使获取失败也打开侧边栏，传递空数组
        this.zEmit('openAlarmEquipmentSidebar', []);
      });
    },
  },
}
</script>
<style lang="less">
@import '../assets/css/equipmentButton.less';
</style>