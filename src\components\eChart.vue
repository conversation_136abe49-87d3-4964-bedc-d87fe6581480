<template>
    <div class="znyEchart" style="width: 100%; height: 100%">
        <div
            v-show="chartData.show"
            ref="chart"
            style="width: 100%; height: 100%; position: relative; left: 0; top: 0"
        ></div>
        <div
            v-show="!chartData.show"
            class="nodata"
        >
            <img src="../assets/image/monitoringCenter/noData.png" alt="" />
            <div>暂无数据</div>
        </div>
    </div>
</template>

<script>
import * as echarts from 'echarts'
import { chart } from '../js/chart.js'

export default {
    props: {
        chartData: {
            type: Object,
            default: () => {
                return {
                    show: false,
                }
            }, // 默认值
        },
        chartType: {
            type: String,
            default: '', //pie:饼图，bar:柱状，line:折线
        },
    },
    data() {
        return {
            timer: null,
            screenWidth: document.body.clientWidth,
            screenHight: document.body.clientHeight,
            timers: null,
            threeDHoveredIndex: null, //3d饼图hovered index
        }
    },
    mounted() {
        // this.redraw();
    },
    methods: {
        getEChartInstance() {
            var instance = echarts.getInstanceByDom(this.$refs['chart'])
            if (!instance) {
                instance = echarts.init(this.$refs['chart'])
            }
            return instance
        },
        redraw() {
            this.$nextTick(() => {
                if (this.chartData.show) {
                    this.getEChartInstance().dispose()
                    this.getEChartInstance().setOption(this.chartData.option)
                    if (this.chartType == 'pie') {
                        this.animate('highlight', 'downplay')
                    } else if (this.chartType == 'bar' || this.chartType == 'line') {
                        this.animate('showTip', 'hideTip')
                    } else if (this.chartType == '3dpie') {
                        this.bindListen()
                    }
                }
            })
        },
        animate(lightType, unLightType) {
            if (!this.chartData.show) return
            var index = 0
            const myChart = this.getEChartInstance()
            let that = this
            emphasis()

            function emphasis() {
                //设置默认选中高亮部分
                if (that.timer) {
                    clearInterval(that.timer)
                }
                that.timer = setInterval(move, 2100)
                function move() {
                    // myChart.dispatchAction({
                    that.getEChartInstance().dispatchAction({
                        type: unLightType,
                        seriesIndex: 0,
                        dataIndex: index - 1,
                    })
                    // console.log('数据',option.series)
                    if (that.chartData.option.series && that.chartData.option.series[0] && that.chartData.option.series[0].data && index > that.chartData.option.series[0].data.length - 1) {
                        index = 0
                    }
                    // myChart.dispatchAction({
                    that.getEChartInstance().dispatchAction({
                        type: lightType,
                        seriesIndex: 0,
                        dataIndex: index,
                    })
                    index++
                    // option && myChart.setOption(option);
                }
            }
            function unemphasis() {
                // 取消之前高亮的图形
                // myChart.dispatchAction({
                that.getEChartInstance().dispatchAction({
                    type: unLightType,
                    seriesIndex: 0,
                    dataIndex: index - 1,
                })
            }
            myChart.on('mouseover', () => {
                if (this.timer) {
                    clearInterval(this.timer)
                    if (index != 0) {
                        unemphasis()
                    }
                }
            })
            myChart.on('mouseout', emphasis)
        },
        bindListen() {
            // 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。
            const that = this
            const myChart = that.getEChartInstance()
            // 监听 mouseover，近似实现高亮（放大）效果
            myChart.on('mouseover', function(params) {
                // 准备重新渲染扇形所需的参数
                let isSelected
                let isHovered
                let startRatio
                let endRatio
                let k
                // 如果触发 mouseover 的扇形当前已高亮，则不做操作
                if (that.threeDHoveredIndex === params.seriesIndex) {
                    return
                    // 否则进行高亮及必要的取消高亮操作
                } else {
                    // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
                    if (that.threeDHoveredIndex !== null) {
                        // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
                        isSelected = that.chartData.option.series[that.threeDHoveredIndex].pieStatus.selected
                        isHovered = false
                        startRatio = that.chartData.option.series[that.threeDHoveredIndex].pieData.startRatio
                        endRatio = that.chartData.option.series[that.threeDHoveredIndex].pieData.endRatio
                        k = that.chartData.option.series[that.threeDHoveredIndex].pieStatus.k
                        // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
                        that.chartData.option.series[
                            that.threeDHoveredIndex
                        ].parametricEquation = chart.getParametricEquation(
                            startRatio,
                            endRatio,
                            isSelected,
                            isHovered,
                            k,
                            50
                        )
                        that.chartData.option.series[that.threeDHoveredIndex].pieStatus.hovered = isHovered
                        // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
                        that.threeDHoveredIndex = null
                    }
                    // 如果触发 mouseover 的扇形不是透明圆环，将其高亮（对 option 更新）
                    if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
                        // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
                        isSelected = that.chartData.option.series[params.seriesIndex].pieStatus.selected
                        isHovered = true
                        startRatio = that.chartData.option.series[params.seriesIndex].pieData.startRatio
                        endRatio = that.chartData.option.series[params.seriesIndex].pieData.endRatio
                        k = that.chartData.option.series[params.seriesIndex].pieStatus.k
                        // 对当前点击的扇形，执行高亮操作（对 option 更新）
                        that.chartData.option.series[
                            params.seriesIndex
                        ].parametricEquation = chart.getParametricEquation(
                            startRatio,
                            endRatio,
                            isSelected,
                            isHovered,
                            k,
                            80
                        )
                        that.chartData.option.series[params.seriesIndex].pieStatus.hovered = isHovered
                        // 记录上次高亮的扇形对应的系列号 seriesIndex
                        that.threeDHoveredIndex = params.seriesIndex
                    }
                    // 使用更新后的 option，渲染图表
                    myChart.setOption(that.chartData.option)
                }
            })
            // 修正取消高亮失败的 bug
            myChart.on('globalout', function() {
                // 准备重新渲染扇形所需的参数
                let isSelected
                let isHovered
                let startRatio
                let endRatio
                let k
                if (that.threeDHoveredIndex !== null) {
                    // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
                    isSelected = that.chartData.option.series[that.threeDHoveredIndex].pieStatus.selected
                    isHovered = false
                    k = that.chartData.option.series[that.threeDHoveredIndex].pieStatus.k
                    startRatio = that.chartData.option.series[that.threeDHoveredIndex].pieData.startRatio
                    endRatio = that.chartData.option.series[that.threeDHoveredIndex].pieData.endRatio
                    // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
                    that.chartData.option.series[
                        that.threeDHoveredIndex
                    ].parametricEquation = chart.getParametricEquation(
                        startRatio,
                        endRatio,
                        isSelected,
                        isHovered,
                        k,
                        50
                    )
                    that.chartData.option.series[that.threeDHoveredIndex].pieStatus.hovered = isHovered
                    // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
                    that.threeDHoveredIndex = null
                }
                // 使用更新后的 option，渲染图表
                myChart.setOption(that.chartData.option)
            })
        },
    },
    beforeDestroy() {
        this.getEChartInstance().dispose()
        this.timer && clearInterval(this.timer)
    },
    watch: {
        chartData() {
            this.redraw()
        },
    },
}
</script>
<style lang="less">
.znyEchart {
    .nodata {
        width: 100%;
        height: 90%; 
        position: relative; 
        left: 0; 
        top: 10%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: 400;
        color: rgba(174,203,209,0.3);
        img{
            width:252px;
            height: 150px;
        }
    }
}
</style>
