<template>
    <div class="monitoringCenter">
        <div class="monitoringCenter_left">
            <!-- 智慧物联数据 -->
            <div class="smartIOTData">
                <div class="title smartIOTData_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>智慧物联数据</span>
                </div>
                <div class="smartIOTData_con">
                    <div class="smartIOTData_con_row">
                        <div class="smartIOTData_con_row_item">
                            <div class="item_img">
                                <img src="../assets/image/monitoringCenter/icon1.png" alt="" />
                            </div>
                            <div class="item_text">
                                <div v-if="smartIOTData.temperature==null">--</div>
                                <div v-else>{{smartIOTData.temperature}}℃</div>
                                <div>温度</div>
                            </div>
                        </div>
                        <div class="smartIOTData_con_row_item">
                            <div class="item_img">
                                <img src="../assets/image/monitoringCenter/icon2.png" alt="" />
                            </div>
                            <div class="item_text">
                                <div v-if="smartIOTData.humidity==null">--</div>
                                <div v-else>{{smartIOTData.humidity}}%RH</div>
                                <div>湿度</div>
                            </div>
                        </div>
                    </div>
                    <div class="smartIOTData_con_row2">
                        <div class="smartIOTData_con_row2_item">
                            <div class="item_img">
                                <img src="../assets/image/monitoringCenter/co2.png" alt="" />
                            </div>
                            <div class="item_text">
                                <div v-if="smartIOTData.co2==null">--</div>
                                <div v-else>{{smartIOTData.co2}}ppm</div>
                                <div>二氧化碳含量</div>
                            </div>
                        </div>
                        <div class="smartIOTData_con_row2_item">
                            <div class="item_img">
                                <img src="../assets/image/monitoringCenter/lx.png" alt="" />
                            </div>
                            <div class="item_text">
                                <div v-if="smartIOTData.lightIntensity==null">--</div>
                                <div v-else>{{smartIOTData.lightIntensity}}lx</div>
                                <div>光照强度</div>
                            </div>
                        </div>
                    </div>
                    <div class="smartIOTData_con_row3">
                        <div class="smartIOTData_con_row3_item">
                            <div class="item_img">
                                <img src="../assets/image/monitoringCenter/tuwen.png" alt="" />
                            </div>
                            <div class="item_text">
                                <div v-if="smartIOTData.soilTemperature==null">--</div>
                                <div v-else>{{smartIOTData.soilTemperature}}℃</div>
                                <div>土温</div>
                            </div>
                        </div>
                        <div class="smartIOTData_con_row3_item">
                            <div class="item_img">
                                <img src="../assets/image/monitoringCenter/tushi.png" alt="" />
                            </div>
                            <div class="item_text">
                                <div v-if="smartIOTData.soilHumidity==null">--</div>
                                <div v-else>{{smartIOTData.soilHumidity}}%</div>
                                <div>土湿</div>
                            </div>
                        </div>
                        <div class="smartIOTData_con_row3_item">
                            <div class="item_img">
                                <img src="../assets/image/monitoringCenter/diandao.png" alt="" />
                            </div>
                            <div class="item_text">
                                <div v-if="smartIOTData.conductivity==null">--</div>
                                <div v-else>{{smartIOTData.conductivity}}μS/cm</div>
                                <div>电导率</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
            <!-- 设备报警 -->
            <div class="equipmentAlarm">
                <div class="title equipmentAlarm_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>警情报警</span>
                </div>
                <div class="equipmentAlarm_con">
                    <EquipmentAlarm :type='0' :alarmCollect='alarmCollect'/>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div>
        <div class="monitoringCenter_center">
            <!-- 情况概述 -->
            <div class="situationOverview">
                <div class="title situationOverview_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>情况概述</span>
                </div>
                <div class="situationOverview_con">
                    <div class="situationOverview_con_top">
                        <div class="details">
                            <div v-if="caseLookData.totalArea==null">
                                0亩
                            </div>
                            <div v-else>{{caseLookData.totalArea}}亩</div>
                            <div>基地总面积</div>
                        </div>
                        <!-- <div class="details">
                            <div v-if="caseLookData.grossAssets==null">
                                0元
                            </div>
                            <div v-else>{{caseLookData.grossAssets}}元</div>
                            <div>投资总额</div>
                        </div> -->
                        <div class="details">
                            <div v-if="caseLookData.coverage==null">
                                0%
                            </div>
                            <div v-else>{{caseLookData.coverage}}%</div>
                            <div>智慧农业覆盖</div>
                        </div>
                        <div class="details">
                            <div v-if="caseLookData.annualValue==null">
                                0元
                            </div>
                            <div v-else>{{caseLookData.annualValue}}元</div>
                            <div>作物年产值</div>
                        </div>
                        <div class="details">
                            <div v-if="caseLookData.yield==null">
                                0元
                            </div>
                            <div v-else>{{caseLookData.yield}}元</div>
                            <div>作物总产量</div>
                        </div>
                    </div>
                    <div class="situationOverview_con_bottom">
                        <img src="../assets/image/monitoringCenter/di.png" alt="" />
                    </div>
                </div>
            </div>
            <!-- 智慧农业示范区地图 -->
            <div class="demonstrationMap">
                <div class="title demonstrationMap_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>智能日光温室大棚示范基地</span>
                </div>
                <div class="demonstrationMap_con">
                    <!-- <DemonstrationMap /> -->
                    <znyAMap :type="0" ref="znyAMap"></znyAMap>
                </div>
            </div>
        </div>
        <div class="monitoringCenter_right">
            <!-- 农作物溯源信息 -->
            <div class="traceabilityInfo">
                <div class="title traceabilityInfo_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>设备控制记录</span>
                </div>
                <div class="traceabilityInfo_con">
                    <!-- <znyEchart :chartData="traceabilityInfoOption" :chartType="'bar'"></znyEchart> -->
                    <DeviceControlRecord :record-data="deviceControlData"/>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
            <!-- 电商交易占比 -->
            <div class="transactionProportion">
                <div class="title transactionProportion_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>苗情监测</span>
                </div>
                <div class="transactionProportion_con">
                    <!-- <znyEchart :chartData="transactionProportionOption" :chartType="'pie'"></znyEchart> -->
                    <VideoMonitoring />
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
            <!-- 作物实时价格 -->
            <div class="realPrice">
                <div class="title realPrice_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>历史趋势</span>
                </div>
                <div class="realPrice_con">
                    <HistoryTrend :chart-data="historyTrendData"/>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div>
    </div>
</template>
<script>
// import $ from 'jquery'
import { chart } from '../js/chart.js'
import EquipmentAlarm from '../components/equipmentAlarm.vue'
// import DemonstrationMap from '../components/demonstrationMap.vue'
import znyAMap from '../components/znyAMap.vue';
import DeviceControlRecord from '../components/DeviceControlRecord.vue';
import VideoMonitoring from '../components/VideoMonitoring.vue';
import HistoryTrend from '../components/HistoryTrend.vue';
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService.js';


export default {
    components: {
        EquipmentAlarm,
        // DemonstrationMap,
        znyAMap,
        DeviceControlRecord,
        VideoMonitoring,
        HistoryTrend,
    },
    data() {
        return {
            smartIOTData:{},//智能物联数据
            caseLookData:{},//情况概述
            alarmCollect:{},//报警信息
            deviceControlData: {}, //设备控制记录
            historyTrendData: {}, //历史趋势数据
            classOption: {
                step: 0.5
            },
        }
    },
    mounted() {
        // 获取情况概述数据
        this.getHomeCaseLook();
        
        // 监听地块点击事件，接收物联网数据
        this.$root.$on('plot-iot-data', (iotData) => {
            console.log('接收到智慧物联数据:', iotData);
            this.smartIOTData = iotData;
        });
        
        // 监听地块点击事件，接收报警数据
        this.$root.$on('plot-alarm-data', (alarmData) => {
            console.log('接收到报警数据:', alarmData);
            this.alarmCollect = alarmData;
        });
        
        // 监听地块点击事件，接收设备控制记录数据
        this.$root.$on('plot-device-control-data', (deviceControlData) => {
            console.log('接收到设备控制记录数据:', deviceControlData);
            this.deviceControlData = deviceControlData;
        });
        
        // 监听地块点击事件，接收历史趋势数据
        this.$root.$on('plot-history-trend-data', (historyTrendData) => {
            console.log('接收到历史趋势数据:', historyTrendData);
            this.historyTrendData = historyTrendData;
        });
    },
    methods: {
         // 获取情况概述数据
         getHomeCaseLook(){
            CommonService.homeCaseLook()
            .then(res=>{
                this.caseLookData=res
            })
        },
    },
    beforeDestroy() {
        const that = this;
        Object.assign(that.$data, that.$options.data());
        // 移除事件监听
        this.$root.$off('plot-iot-data');
        this.$root.$off('plot-alarm-data');
        this.$root.$off('plot-device-control-data');
        this.$root.$off('plot-history-trend-data');
    },
}
</script>
<style lang="less">
@import '../assets/css/monitoringCenter.less';
</style>
