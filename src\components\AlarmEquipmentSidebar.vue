<template>
  <div class="alarm-equipment-list" v-if="isVisible">
    <!-- 标题 -->
    <div class="list-header">
      <img src="../assets/image/centralControlPlatform/icon1.png" alt="" />
      <span>报警设备 ({{ alarmEquipmentList.length }})</span>
      <div class="close-btn" @click="closeSidebar">×</div>
    </div>

    <!-- 设备列表 -->
    <div class="equipment-items">
      <div
        v-for="(equipment, index) in alarmEquipmentList"
        :key="equipment.equipmentId || index"
        class="equipment-item"
      >
        <div class="equipment-name">{{ equipment.equipmentName }}</div>
        <div class="alarm-type" v-if="equipment.alarmType">{{ equipment.alarmType }}</div>
        <div class="equipment-info">
          <span v-if="equipment.areaName" class="area-name">{{ equipment.areaName }}</span>
          <span v-if="equipment.equipmentType" class="equipment-type">{{ equipment.equipmentType }}</span>
          <span class="online-status" :class="{ 'online': equipment.online, 'offline': !equipment.online }">
            {{ equipment.online ? '在线' : '离线' }}
          </span>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="alarmEquipmentList.length === 0" class="empty-text">
        暂无报警设备
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'AlarmEquipmentSidebar',
  data() {
    return {
      isVisible: false,
      alarmEquipmentList: []
    }
  },
  mounted() {
    // 监听打开侧边栏事件，接收设备列表数据
    this.listen('openAlarmEquipmentSidebar', (equipmentList) => {
      this.openSidebar(equipmentList)
    })
  },
  methods: {
    // 打开侧边栏
    openSidebar(equipmentList = []) {
      this.isVisible = true
      this.loadAlarmEquipmentData(equipmentList)
    },

    // 关闭侧边栏
    closeSidebar() {
      this.isVisible = false
    },

    // 加载报警设备数据
    loadAlarmEquipmentData(equipmentList = []) {
      if (equipmentList && equipmentList.length > 0) {
        // 使用传递过来的真实数据
        this.alarmEquipmentList = equipmentList.map(equipment => ({
          equipmentName: equipment.equipmentName || equipment.name || '未知设备',
          equipmentId: equipment.equipmentId || equipment.id,
          alarmType: equipment.alarmType || '设备报警',
          equipmentType: equipment.equipmentType || equipment.type,
          areaName: equipment.areaName || '未知区域',
          online: equipment.online,
          alarmStatus: equipment.alarmStatus
        }))
      } else {
        // 如果没有数据，显示空列表
        this.alarmEquipmentList = []
      }
    }
  },
  beforeDestroy() {
    this.removeAllListener();
  }
}
</script>

<style lang="less" scoped>
.alarm-equipment-list {
  position: absolute;
  bottom: 43px;
  left: 537px;
  width: 280px;
  max-height: 400px;
  background: rgba(0, 61, 66, 0.95);
  border: 1px solid rgba(0, 244, 253, 0.3);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  backdrop-filter: blur(10px);

  .list-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid rgba(0, 244, 253, 0.2);
    color: #DFEEF3;
    font-size: 14px;
    font-weight: bold;

    img {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .close-btn {
      margin-left: auto;
      cursor: pointer;
      color: rgba(223, 238, 243, 0.6);
      font-size: 18px;
      line-height: 1;
      padding: 2px 6px;
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #DFEEF3;
      }
    }
  }

  .equipment-items {
    max-height: 320px;
    overflow-y: auto;
    padding: 8px;

    .equipment-item {
      padding: 8px 12px;
      margin-bottom: 6px;
      background: rgba(1, 18, 21, 0.6);
      border-radius: 4px;
      border-left: 3px solid #FF4757;

      .equipment-name {
        color: #DFEEF3;
        font-size: 13px;
        font-weight: bold;
        margin-bottom: 4px;
      }

      .alarm-type {
        color: #FF4757;
        font-size: 11px;
        margin-bottom: 4px;
      }

      .equipment-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 10px;

        .area-name {
          color: #00F4FD;
        }

        .equipment-type {
          color: rgba(223, 238, 243, 0.7);
        }

        .online-status {
          padding: 1px 4px;
          border-radius: 2px;
          font-size: 9px;

          &.online {
            background: rgba(0, 244, 61, 0.2);
            color: #00F43D;
          }

          &.offline {
            background: rgba(255, 71, 87, 0.2);
            color: #FF4757;
          }
        }
      }
    }

    .empty-text {
      text-align: center;
      padding: 20px;
      color: rgba(223, 238, 243, 0.5);
      font-size: 12px;
    }

    /* 滚动条样式 */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 244, 253, 0.3);
      border-radius: 2px;

      &:hover {
        background: rgba(0, 244, 253, 0.5);
      }
    }
  }
}
</style>
