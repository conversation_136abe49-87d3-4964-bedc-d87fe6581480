#regionalThresholdSetting {
    height: 100%;
    .regional {
        display: flex;
        width: 100%;
        height: 100%;
        .regional_left {
            width: 160px;
            height: 100%;
            background: #ffffff;
            opacity: 1;
            border-radius: 0px;
            border-right: 1px solid #DBE7EE;
            .regional_left_table {
                height: 83%;
                overflow-y: scroll;
            }
            .regional_left_box {
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 60px;
                background: #ffffff;
                opacity: 1;
                border-radius: 0px;
                text-align: center;
                .regional_left_text {
                    width: 98%;
                    height: 100%;
                    line-height: 60px;
                }
                .regional_left_img {
                    width: 8px;
                    height: 8px;
                    margin-right: 26px;
                    img {
                        position: absolute;
                        // width: 100%;
                        // height: 100%;
                        // right: 26px;
                    }
                }
            }
            .itemActive {
                background: #edf4fb;
                opacity: 1;
            }
            .systemSearchButtonStyle2 {
                width: 140px;
                height: 40px;
                background: #0093bc;
                opacity: 1;
                border-radius: 6px;
                margin: auto;
                margin-top: 20px;
            }
        }

        // 生长阶段
        .growth_stage_panel {
            width: 112px;
            height: 100%;
            background: #ffffff;
            opacity: 1;
            border-radius: 0px;
            margin-left: 0px;

            .growth_stage_header {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 60px;
                background: #ffffff;
                opacity: 1;
                border-radius: 0px;
                text-align: center;
                font-size: 16px;
                font-family: Source Han Sans CN;
                font-weight: bold;
                color: #333333;
            }

            .growth_stage_list {
                height: 83%;
                overflow-y: scroll;

                .growth_stage_item {
                    position: relative;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 100%;
                    height: 60px;
                    background: #ffffff;
                    opacity: 1;
                    border-radius: 0px;
                    text-align: center;

                    .growth_stage_text {
                        width: 98%;
                        height: 100%;
                        line-height: 60px;
                        font-size: 16px;
                        font-family: Source Han Sans CN;
                        font-weight: 400;
                        color: #333333;
                    }

                    &.itemActive {
                        background: #edf4fb;
                        opacity: 1;
                    }

                    .thread {
                        position: absolute;
                        left: 0px;
                        width: 6px;
                        height: 60px;
                        background: linear-gradient(150deg, #12aca7 0%, #0093bc 100%);
                        opacity: 1;
                        border-radius: 0px 5px 5px 0px;
                    }
                }
            }
        }

        .regional_right {
            width: 1344px;
            height: 790px;
            background: #ffffff;
            margin: 32px;
            .regional_right_box {
                display: flex;
                flex-direction: column;
                height: 100%;

                .systemFormStyle {
                    height: 48px;
                }

                // 可滚动的内容区域
                .regional_right_content_wrapper {
                    flex: 1; 
                    overflow-y: auto;
                    padding-right: 10px; 

                    // 滚动条样式
                    &::-webkit-scrollbar {
                        width: 6px;
                    }

                    &::-webkit-scrollbar-track {
                        background: #f1f1f1;
                        border-radius: 3px;
                    }

                    &::-webkit-scrollbar-thumb {
                        background: #c1c1c1;
                        border-radius: 3px;

                        &:hover {
                            background: #a8a8a8;
                        }
                    }
                }

                .regional_right_button {
                    display: flex;
                    .regional_right_systemSearchButtonStyle2 {
                        width: 140px;
                        height: 40px;
                        background: #0093bc;
                        opacity: 1;
                        border-radius: 6px;
                        // margin: auto;
                        margin-top: 20px;
                        margin-left: 20px;
                    }
                }
                .regional_right_button1 {
                    display: flex;
                    justify-content: center;
                    margin-top: auto;
                    padding: 20px 0; 
                    flex-shrink: 0; 
                    .regional_right_systemSearchButtonStyle2 {
                        width: 140px;
                        height: 40px;
                        background: #0093bc;
                        opacity: 1;
                        border-radius: 6px;
                        // margin: auto;
                        margin-top: 20px;
                        margin-left: 20px;
                    }
                    .regional_right_systemSearchButtonStyle1 {
                        width: 140px;
                        height: 40px;
                        background: #cccccc;
                        opacity: 1;
                        border-radius: 6px;
                        // margin: auto;
                        margin-top: 20px;
                        margin-left: 20px;
                        .el-button {
                            background: #cccccc;
                        }
                    }
                }
                .regional_right_relevance {
                    display: flex;
                    margin-top: 30px;
                    .regional_right_relevance_text {
                        line-height: 48px;
                        font-size: 16px;
                        font-family: Source Han Sans CN;
                        font-weight: bold;
                        color: #333333;
                        opacity: 1;
                        margin-left: 20px;
                        margin-right: 15px;
                    }
                    .regional_right_relevance_text1 {
                        line-height: 48px;
                        font-size: 16px;
                        font-family: Source Han Sans CN;
                        font-weight: bold;
                        color: #333333;
                        opacity: 1;

                        .growth_stage_info {
                            color: #0093bc;
                            font-weight: normal;
                            margin-left: 8px;
                        }
                    }
                }

                .regional_right_headline {
                    font-size: 16px;
                    font-family: Source Han Sans CN;
                    font-weight: 500;
                    color: #333333;
                    opacity: 1;
                    margin-top: 26px;
                    margin-left: 20px;
                    img {
                        margin-left: 10px;
                        vertical-align: middle;
                    }
                }
                .regional_right_matter {
                    display: flex;
                    padding-left: 20px;
                    margin-top: 20px;
                    .regional_right_matter_box {
                        .regional_right_matter_box_header {
                            font-size: 16px;
                            font-family: Source Han Sans CN;
                            font-weight: 500;
                            color: #333333;
                            opacity: 1;
                        }
                        .regional_right_matter_box_row {
                            display: flex;
                            margin-top: 16px;
                            .regional_right_matter_box_row_text {
                                font-size: 16px;
                                font-family: Source Han Sans CN;
                                font-weight: 400;
                                color: #666666;
                                opacity: 1;
                                line-height: 40px;
                            }
                        }
                    }

                    .border {
                        width: 1px;
                        height: 85px;
                        background: #dfdfdf;
                        opacity: 1;
                        border-radius: 0px;
                        margin-left: 16px;
                        margin-right: 24px;
                    }
                }
                .regional_right_content {
                    display: flex;
                    flex-wrap: wrap;
                    .regional_right_content_row1 {
                        display: flex;
                        margin-top: 16px;
                        margin-left: 15px; 
                        .text {
                            font-size: 16px;
                            font-family: Source Han Sans CN;
                            font-weight: 400;
                            line-height: 40px;
                            color: #666666;
                            opacity: 1;
                            img {
                                margin-left: 10px;
                                vertical-align: middle;
                            }
                        }
                        .right_input {
                            width: 80px;
                            height: 40px;
                        }
                        .margin_left {
                            margin-left: 8px;
                        }
                        .margin_right {
                            margin-right: 8px; 
                        }
                        .margin_right_left {
                            margin-right: 8px; 
                            margin-left: 8px; 
                        }
                    }
                }

                // 虫情报警阈值
                .regional_right_content {
                    .regional_right_content_row1 {
                        // 虫情下拉选择框
                        .systemFormStyle {
                            width: 130px !important;

                            .el-select {
                                width: 130px !important;
                            }
                        }

                        // 虫情输入框
                        .right_input {
                            width: 70px !important;

                            .el-input {
                                width: 70px !important;
                            }
                        }
                    }
                }

                .regional_right_content2 {
                    display: flex;
                    // justify-content: space-between;
                    margin-left: 20px;

                    flex-wrap: wrap;
                    .regional_right_content_row1 {
                        display: flex;
                        margin-top: 16px;
                        .text {
                            font-size: 16px;
                            font-family: Source Han Sans CN;
                            font-weight: 400;
                            line-height: 40px;
                            color: #666666;
                            opacity: 1;
                            img {
                                margin-left: 10px;
                                vertical-align: middle;
                            }
                        }
                        .right_input {
                            width: 80px;
                            height: 40px;
                        }
                        .margin_left {
                            margin-left: 14px;
                        }
                        .margin_right {
                            margin-right: 14px;
                        }
                        .margin_right_left {
                            margin-right: 14px;
                            margin-left: 14px;
                        }
                    }
                }
            }
        }

        .cursor {
            cursor: pointer;
        }
        .thread {
            position: absolute;
            left: 0px;
            width: 6px;
            height: 60px;
            background: linear-gradient(150deg, #12aca7 0%, #0093bc 100%);
            opacity: 1;
            border-radius: 0px 5px 5px 0px;
        }
    }
    .close {
        position: absolute;
        top: 16px;
        right: 16px;
        cursor: pointer;
    }
    .right_input {
        width: 126px;
        height: 40px;
        .el-input__suffix {
            line-height: 40px;
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: 400;
           
            color: #999999;
            opacity: 1;
            right: 12px;
        }
    }
    .margin_left {
        margin-left: 16px;
    }
    .margin_right {
        margin-right: 24px;
    }
    .margin_right_left {
        margin-right: 24px;
        margin-left: 16px;
    }
    .btnBox {
        display: flex;
        justify-content: center;
        .btnItem {
            width: 160px;
            height: 40px;
        }
        .systemSearchButtonStyle2 {
            margin-left: 30px;
        }
    }
}
