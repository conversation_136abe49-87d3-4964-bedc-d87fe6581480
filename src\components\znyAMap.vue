<template>
    <!-- 卫星地图 -->
    <div class="AMapContainer">
        <zny-map
            ref="aMap"
            style="width: 100%; height: 100%"
            :map-options="amapOptions"
            :max-zoom="20"
            :roadnet="true"
        ></zny-map>
        <!-- 地块选择按钮组 -->
        <div class="area-selector" v-if="areaCropList && areaCropList.length > 0">
            <div class="area-title">地块选择</div>
            <div class="area-buttons">
                <div 
                    v-for="(area, index) in areaCropList" 
                    :key="area.areaId"
                    class="area-button"
                    :class="{ active: selectedAreaIndex === index }"
                    @click="switchToArea(index)"
                >
                    {{ area.areaName || '地块' + area.areaId }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapHandle } from '../js/mapHandle.js'
import HomePageService from '../jaxrs/concrete/com.zny.ia.api.ConcreteService.HomePageService.js'
import EquipmentCtrlStateService from '../jaxrs/concrete/com.zny.ia.api.ConcreteService.EquipmentCtrlStateService.js'
import WeatherService from '../jaxrs/concrete/com.zny.ia.api.WeatherService.js'

export default {
    props: {
        type: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            amapOptions: {
                zoom: 15,
                center: [117.12665, 36.6584],
                amapTileUrl: localStorage.getItem('amapTileUrl'),
            },
            aMap: null,
            areaCropList: [], // 区域列表
            selectedAreaIndex: 0, // 当前选择的区域索引
            currentPolygon: null, // 当前渲染的多边形
            currentSelectedAreaId: null, // 当前选中的地块ID，用于切换选中状态
        }
    },
    mounted() {
        this.aMap = this.$refs['aMap']
        let scaleValue = localStorage.getItem('scaleValue')
        if (scaleValue) this.aMap.descale(scaleValue)
        this.listen('scaleChangeWatch', val => {
            this.aMap.descale(val.scale)
        })

        this.findAreaCrop()
    },
    methods: {
        findAreaCrop() {
            HomePageService.findAreaCrop().then(res => {
                this.areaCropList = res
                
                // 预处理每个地块数据，解析边界坐标
                this.areaCropList.forEach(block => {
                    let arr = block.border.split(';')
                    let ll = []
                    arr.forEach(e => {
                        let lat, lng
                        lat = parseFloat(e.split(',')[0])
                        lng = parseFloat(e.split(',')[1])
                        ll.push([lng, lat])
                    })
                    let polygonPath = [{ border: ll }]
                    block.polygonPath = polygonPath
                })
                
                // 默认选中第一个地块并展示其数据
                if (this.areaCropList.length > 0) {
                    this.selectedAreaIndex = 0
                    this.currentSelectedAreaId = this.areaCropList[0].areaId
                    this.renderSelectedArea(0)

                    // 初始化时默认展示第一个地块的数据
                    if (this.type === 0) {
                        // 监控中心的初始化数据
                        this.loadIotData(this.areaCropList[0].areaId);
                        this.loadAlarmData(this.areaCropList[0].areaId);
                        this.loadDeviceControlRecordData(this.areaCropList[0].areaId);

                        // 加载真实的历史趋势数据
                        this.loadHistoryTrendData(this.areaCropList[0].areaId);

                        // 发送初始区域选择变化事件给视频监控组件
                        this.$root.$emit('video-monitoring-area-change', this.areaCropList[0].areaId);
                    } else {
                        // 中控平台的初始化数据
                        // 发送初始地块选择变化事件给苗情监测组件
                        this.$root.$emit('seedling-monitoring-area-change', this.areaCropList[0].areaId);

                        // 发送初始地块选择信息给中控平台（用于历史数据弹窗）
                        this.$root.$emit('area-selection-change', {
                            areaId: this.areaCropList[0].areaId,
                            areaName: this.areaCropList[0].areaName
                        });

                        this.loadIotData(this.areaCropList[0].areaId);
                        this.loadAlarmData(this.areaCropList[0].areaId);
                        this.loadDeviceOperationData(this.areaCropList[0].areaId);

                        // 发送初始地块选择变化事件给历史数据弹窗
                        this.zEmit('areaSelectionChangeForHistory', {
                            areaId: this.areaCropList[0].areaId,
                            areaName: this.areaCropList[0].areaName
                        });
                    }
                }
            })
        },
        
        // 渲染选中的地块
        renderSelectedArea(index) {
            if (index < 0 || index >= this.areaCropList.length) return;
            
            const map = this.$refs['aMap']
            const block = this.areaCropList[index]
            
            // 如果已有多边形，先移除
            if (this.currentPolygon) {
                map.removePolygon()
                this.currentPolygon = null
            }
            
            // 渲染新的多边形
            this.currentPolygon = mapHandle.addPlatPolygon(map, block, {
                keyOfGeo: 'geometry',
                opacity: 0.5,
                selectOpacity: 1,
                onClick: () => {
                    // 检查是否点击的是当前已选中的地块
                    if (this.currentSelectedAreaId === block.areaId) {
                        // 如果是同一个地块，取消选中
                        console.log('取消选中地块:', block.areaId);
                        this.currentSelectedAreaId = null;
                        if (this.currentPolygon) {
                            this.currentPolygon.unselect();
                        }
                        return;
                    }

                    // 选中新的地块
                    this.currentSelectedAreaId = block.areaId;
                    this.selectedAreaIndex = index;

                    // 根据type区分不同的点击事件处理
                    if (this.type === 0) {
                        // 监控中心的点击事件处理 - 发送事件并传递数据
                        console.log('监控中心地块点击:', block.areaId, index);

                        // 加载真实的智慧物联数据、报警数据和设备控制记录数据
                        this.loadIotData(block.areaId);
                        this.loadAlarmData(block.areaId);
                        this.loadDeviceControlRecordData(block.areaId);

                        // 加载真实的历史趋势数据
                        this.loadHistoryTrendData(block.areaId);

                        // 发送区域选择变化事件给视频监控组件
                        this.$root.$emit('video-monitoring-area-change', block.areaId);

                        // 可以在这里触发监控中心特有的弹窗或事件
                        this.zEmit('monitorCenterAreaClick', block);
                    } else {
                        // 中控平台的点击事件处理
                        console.log('中控平台地块点击:', block.areaId);

                        // 发送地块选择变化事件给苗情监测组件
                        this.$root.$emit('seedling-monitoring-area-change', block.areaId);

                        // 发送地块选择信息给中控平台（用于历史数据弹窗）
                        this.$root.$emit('area-selection-change', {
                            areaId: block.areaId,
                            areaName: block.areaName
                        });

                        // 加载真实的智慧物联数据、报警数据和设备操作数据
                        this.loadIotData(block.areaId);
                        this.loadAlarmData(block.areaId);
                        this.loadDeviceOperationData(block.areaId);

                        // 可以在这里触发中控平台特有的弹窗或事件
                        this.zEmit('controlPlatformAreaClick', block);
                    }
                },
                onUnselected: () => {
                    console.log('地块已取消选中');
                }
            })
            
            // 设置地图中心和视图
            if (block.polygonPath && block.polygonPath[0].border && block.polygonPath[0].border.length > 0) {
                // 计算中心点
                const centerPoint = this.calculatePolygonCenter(block.polygonPath[0].border)
                map.center(centerPoint)
                
                // 设置适当的缩放级别
                setTimeout(() => {
                    map.setFitView()
                }, 100)
            }
        },
        
        // 切换到指定地块
        switchToArea(index) {
            if (index >= 0 && index < this.areaCropList.length) {
                this.selectedAreaIndex = index
                this.renderSelectedArea(index)
            }
        },

        // 根据地块ID获取地块名称
        getAreaNameById(areaId) {
            const area = this.areaCropList.find(item => item.areaId === areaId);
            return area ? area.areaName : '暂无数据';
        },

        // 加载智慧物联数据
        loadIotData(areaId) {
            console.log('加载智慧物联数据:', areaId);
            // 调用智慧物联数据接口
            HomePageService.homeUnionAIData(areaId).then(res => {
                console.log('智慧物联数据:', res);
                this.$root.$emit('plot-iot-data', res);
                
                // 如果是中控平台，同时发送智慧物联数据更新事件给历史数据弹窗
                if (this.type === 1) {
                    this.$root.$emit('smartIOTDataUpdate', {
                        smartIOTData: res,
                        collectDateTime: res.createTime || ''
                    });

                    // 同时发送地块选择变化事件给历史数据弹窗（用于更新弹窗标题和图表）
                    this.zEmit('areaSelectionChangeForHistory', {
                        areaId: areaId,
                        areaName: this.getAreaNameById(areaId)
                    });
                }
            }).catch(err => {
                console.error('获取智慧物联数据失败:', err);
            });
        },

        // 加载报警数据
        loadAlarmData(areaId) {
            console.log('加载报警数据:', areaId);

            // 调用报警汇总接口
            HomePageService.alarmCollect(areaId).then(res => {
                console.log('报警数据:', res);
                this.$root.$emit('plot-alarm-data', res);
            }).catch(err => {
                console.error('获取报警数据失败:', err);
            });
        },

        // 加载设备操作数据
        loadDeviceOperationData(areaId) {
            console.log('加载设备操作数据:', areaId);

            // 调用设备控制项接口
            EquipmentCtrlStateService.findEquipmentCtrlStateListByAreaId(areaId).then(res => {
                console.log('设备操作数据:', res);
                this.$root.$emit('plot-device-operation-data', res);
            }).catch(err => {
                console.error('获取设备操作数据失败:', err);
            });
        },

        // 加载设备控制记录数据（动态前30条）
        loadDeviceControlRecordData(areaId) {
            console.log('加载设备控制记录数据:', areaId);

            // 调用设备控制状态动态前30条接口
            HomePageService.findEquipmentCtrlStateDynamicTop30List(areaId).then(res => {
                console.log('设备控制记录数据:', res);
                const formattedData = {
                    recordList: res.map(item => ({
                        deviceName: item.name,
                        controlValue: item.changeContent || '--',
                        controlTime: item.changeTime || '--'
                    }))
                };
                this.$root.$emit('plot-device-control-data', formattedData);
            }).catch(err => {
                console.error('获取设备控制记录数据失败:', err);
                // 如果接口失败，发送空数据
                this.$root.$emit('plot-device-control-data', { recordList: [] });
            });
        },
        
        
        // 加载真实的历史趋势数据
        loadHistoryTrendData(areaId) {
            console.log('加载历史趋势数据:', areaId);
            
            //获取近7天的数据
            const po = {
                areaId: areaId,
                showType: 1 // 1表示近7天
            };
            
            // 并行调用温湿度和光照度接口
            Promise.all([
                WeatherService.temperatureChart(po),
                WeatherService.illuminanceChart(po)
            ]).then(([temperatureData, illuminanceData]) => {
                console.log('温湿度数据:', temperatureData);
                console.log('光照度数据:', illuminanceData);
                
                // 格式化数据为历史趋势图表所需格式
                const historyTrendData = this.formatHistoryTrendData(temperatureData, illuminanceData);
                
                // 发送真实数据
                this.$root.$emit('plot-history-trend-data', historyTrendData);
            }).catch(err => {
                console.error('获取历史趋势数据失败:', err);
                
                // 如果接口失败，发送空数据
                const emptyData = {
                    dates: [],
                    temperature: [],
                    humidity: [],
                    lightIntensity: []
                };
                this.$root.$emit('plot-history-trend-data', emptyData);
            });
        },
        
        // 格式化历史趋势数据
        formatHistoryTrendData(temperatureData, illuminanceData) {
            // 处理温湿度数据
            const tempData = temperatureData && temperatureData.weatherChartVoList ? temperatureData.weatherChartVoList : [];
            // 处理光照度数据
            const lightData = illuminanceData && illuminanceData.illuminanceInfo ? illuminanceData.illuminanceInfo : [];

            // 如果没有数据，返回空数组
            if (tempData.length === 0 && lightData.length === 0) {
                return {
                    dates: [],
                    temperature: [],
                    humidity: [],
                    lightIntensity: []
                };
            }

            // 直接使用接口返回的时间和数据
            const dates = tempData.length > 0 ?
                tempData.map(item => item.showTime || '') :
                lightData.map(item => item.showTime || '');

            const temperature = tempData.map(item => item.temperatureAvg || 0);
            const humidity = tempData.map(item => item.humidityAvg || 0);
            const lightIntensity = lightData.map(item => item.illuminanceAvg || 0);

            return {
                dates,
                temperature,
                humidity,
                lightIntensity
            };
        },



         // 计算多边形中心点
        calculatePolygonCenter(points) {
            if (!points || points.length === 0) return [117.12665, 36.6584]
            
            // 简单方法：取多边形所有点的平均值作为中心点
            let sumX = 0, sumY = 0
            points.forEach(point => {
                sumX += point[0]
                sumY += point[1]
            })
            
            return [sumX / points.length, sumY / points.length]
        },
    },
    beforeDestroy() {
        this.removeAllListener()
    },
}
</script>
<style lang="less">
@import '../assets/css/znyAMap.less';

.AMapContainer {
    position: relative;
    width: 100%;
    height: 100%;
}

.area-selector {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    padding: 10px;
    z-index: 100;
    min-width: 120px;
}

.area-title {
    color: white;
    font-size: 14px;
    padding-bottom: 8px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    margin-bottom: 8px;
}

.area-buttons {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.area-button {
    padding: 6px 10px;
    background: rgba(30, 30, 30, 0.8);
    color: white;
    border-radius: 3px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
        background: rgba(60, 60, 60, 0.9);
    }
    
    &.active {
        background: rgba(24, 144, 255, 0.8);
    }
}
</style>
