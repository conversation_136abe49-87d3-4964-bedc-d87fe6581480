<template>
  <div class="smartIOTHistoryDialog dialogStyle">
    <el-dialog
      :title="title"
      width="74.6%"
      :visible.sync="smartIOTHistoryDialog"
      :show-close="false"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @open='handleOpen'
      @close='handleClose'>
      <div class="line"></div>
      <div class="close" @click="closeDialog">
        <img src="../../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="time">
        数据采集时间：{{collectDateTime || '暂无数据'}}
      </div>
      <div class="handleBox">
        <div class="handleBox_item searchButtonStyle">
          <el-dropdown @command="handleReportCommand" trigger="click">
            <el-button>
              汇报更新<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="meteorology">气象数据汇报</el-dropdown-item>
              <el-dropdown-item command="soil">土壤数据汇报</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 数据展示区域 -->
      <div class="dataList">
        <div class="list1">
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/monitoringCenter/icon1.png" alt="">
            </div>
            <div class="item_text">
              <div v-if="smartIOTData.temperature==null">--</div>
              <div v-else>{{smartIOTData.temperature}}℃</div>
              <div>温度</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/monitoringCenter/icon2.png" alt="">
            </div>
            <div class="item_text">
              <div v-if="smartIOTData.humidity==null">--</div>
              <div v-else>{{smartIOTData.humidity}}%RH</div>
              <div>湿度</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/monitoringCenter/co2.png" alt="">
            </div>
            <div class="item_text">              
              <div v-if="smartIOTData.co2==null">--</div>
              <div v-else>{{smartIOTData.co2}}ppm</div>
              <div>二氧化碳浓度</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/monitoringCenter/lx.png" alt="">
            </div>
            <div class="item_text">
              <div v-if="smartIOTData.lightIntensity==null">--</div>
              <div v-else>{{smartIOTData.lightIntensity}}lux</div>
              <div>光照度</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/monitoringCenter/tuwen.png" alt="">
            </div>
            <div class="item_text">
              <div v-if="smartIOTData.soilTemperature==null">--</div>
              <div v-else>{{smartIOTData.soilTemperature}}℃</div>
              <div>土温</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/monitoringCenter/tushi.png" alt="">
            </div>
            <div class="item_text">
              <div v-if="smartIOTData.soilHumidity==null">--</div>
              <div v-else>{{smartIOTData.soilHumidity}}%</div>
              <div>土湿</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/monitoringCenter/diandao.png" alt="">
            </div>
            <div class="item_text">
              <div v-if="smartIOTData.conductivity==null">--</div>
              <div v-else>{{smartIOTData.conductivity}}μS/cm</div>
              <div>电导率</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="chartList">
        <div class="chartList_left">
          <!-- 空气温湿度变化曲线 -->
          <div class="chartList_item">
            <div class="chartList_item_title">空气温湿度变化曲线</div>
            <div class="chartList_item_checkBtn">
              <div class="checkBtn1 checkBtn" @click="airTHTimeCheck(0)" :class="{active1:airTHTimeActive==0}">
                今天
              </div>
              <div class="checkBtn2 checkBtn" @click="airTHTimeCheck(1)" :class="{active2:airTHTimeActive==1}">
                近七天
              </div>
              <div class="checkBtn2 checkBtn" @click="airTHTimeCheck(2)" :class="{active2:airTHTimeActive==2}">
                近30天
              </div>
              <div class="checkBtn2 checkBtn" @click="openHistoryDialog()" :class="{active2:airTHTimeActive==3}">
                更多
              </div>
            </div>
            <div class="chartList_item_chart" style="height:280px">
              <znyEchart :chartData="airTHLineOption" :chartType="'line'"></znyEchart>
            </div>
          </div>
          
          <!-- 二氧化碳变化曲线 -->
          <div class="chartList_item">
            <div class="chartList_item_title">二氧化碳变化曲线</div>
            <div class="chartList_item_checkBtn">
              <div class="checkBtn1 checkBtn" @click="co2TimeCheck(0)" :class="{active1:co2TimeActive==0}">
                今天
              </div>
              <div class="checkBtn2 checkBtn" @click="co2TimeCheck(1)" :class="{active2:co2TimeActive==1}">
                近七天
              </div>
              <div class="checkBtn2 checkBtn" @click="co2TimeCheck(2)" :class="{active2:co2TimeActive==2}">
                近30天
              </div>
              <div class="checkBtn2 checkBtn" @click="openHistoryDialog()" :class="{active2:co2TimeActive==3}">
                更多
              </div>
            </div>
            <div class="chartList_item_chart" style="height:280px;">
              <znyEchart :chartData="co2LineOption" :chartType="'line'"></znyEchart>
            </div>
          </div>

          <!-- 光照度变化曲线 -->
          <div class="chartList_item">
            <div class="chartList_item_title">光照度变化曲线</div>
            <div class="chartList_item_checkBtn">
              <div class="checkBtn1 checkBtn" @click="illuminanceCheck(0)" :class="{active1:illuminanceActive==0}">
                今天
              </div>
              <div class="checkBtn2 checkBtn" @click="illuminanceCheck(1)" :class="{active2:illuminanceActive==1}">
                近七天
              </div>
              <div class="checkBtn2 checkBtn" @click="illuminanceCheck(2)" :class="{active2:illuminanceActive==2}">
                近30天
              </div>
              <div class="checkBtn2 checkBtn" @click="openHistoryDialog()" :class="{active2:illuminanceActive==3}">
                更多
              </div>
            </div>
            <div class="chartList_item_chart" style="height:280px;">
              <znyEchart :chartData="illuminanceLineOption" :chartType="'line'"></znyEchart>
            </div>
          </div>
        </div>
        
        <div class="chartList_right">
          <!-- 土壤温湿度变化曲线 -->
          <div class="chartList_item">
            <div class="chartList_item_title">土壤温湿度变化曲线</div>
            <div class="chartList_item_checkBtn">
              <div class="checkBtn2 checkBtn" @click="soilTHTimeCheck(1)" :class="{active2:soilTHTimeActive==1}">
                近七天
              </div>
              <div class="checkBtn2 checkBtn" @click="soilTHTimeCheck(2)" :class="{active2:soilTHTimeActive==2}">
                近30天
              </div>
              <div class="checkBtn2 checkBtn" @click="openHistoryDialog()" :class="{active2:soilTHTimeActive==3}">
                更多
              </div>
            </div>
            <div class="chartList_item_chart" style="height:430px">
              <znyEchart :chartData="soilTHLineOption" :chartType="'line'"></znyEchart>
            </div>
          </div>

          <!-- 电导率变化曲线 -->
          <div class="chartList_item">
            <div class="chartList_item_title">电导率变化曲线</div>
            <div class="chartList_item_checkBtn">
              <div class="checkBtn2 checkBtn" @click="conductivityCheck(1)" :class="{active2:conductivityActive==1}">
                近七天
              </div>
              <div class="checkBtn2 checkBtn" @click="conductivityCheck(2)" :class="{active2:conductivityActive==2}">
                近30天
              </div>
              <div class="checkBtn2 checkBtn" @click="openHistoryDialog()" :class="{active2:conductivityActive==3}">
                更多
              </div>
            </div>
            <div class="chartList_item_chart" style="height:430px">
              <znyEchart :chartData="conductivityLineOption" :chartType="'line'"></znyEchart>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {chart} from '../../js/chart.js';
import WeatherService from '../../jaxrs/concrete/com.zny.ia.api.WeatherService.js';
import SoilService from '../../jaxrs/concrete/com.zny.ia.api.SoilService.js';
import {gettime} from '../../js/getTime.js';

export default {
  name: 'SmartIOTHistoryDialog',
  data(){
    return{
      smartIOTHistoryDialog: false,
      areaId: '',
      title: "",
      collectDateTime: '',
      smartIOTData: {}, // 智慧物联实时数据
      
      // 空气温湿度
      airTHTimeActive: 0,
      airTHLineOption: {
        show: true,
        option: {},
      },
      
      // 二氧化碳
      co2TimeActive: 0,
      co2LineOption: {
        show: true,
        option: {},
      },
      
      // 光照度
      illuminanceActive: 0,
      illuminanceLineOption: {
        show: true,
        option: {},
      },
      
      // 土壤温湿度
      soilTHTimeActive: 1,
      soilTHLineOption: {
        show: true,
        option: {},
      },
      
      // 电导率
      conductivityActive: 1,
      conductivityLineOption: {
        show: true,
        option: {},
      },
    }
  },
  mounted(){
    // 监听弹窗打开事件
    this.listen('smartIOTHistoryDialogOpen', (block) => {
      this.areaId = block.areaId || ''
      this.title = block.areaName || '玉米种植区'
      this.smartIOTData = block.smartIOTData || {}
      this.collectDateTime = block.collectDateTime || ''
      this.smartIOTHistoryDialog = true
    })
    
    // 监听智慧物联数据更新
    this.listen('smartIOTDataUpdate', (data) => {
      this.smartIOTData = data.smartIOTData || {}
      this.collectDateTime = data.collectDateTime || ''
      // 如果弹窗已经打开，刷新所有图表数据
      if (this.smartIOTHistoryDialog) {
        this.initAllCharts()
      }
    })

    // 监听地块选择变化，更新弹窗标题和地块ID
    this.listen('areaSelectionChangeForHistory', (data) => {
      this.areaId = data.areaId || ''
      this.title = data.areaName || '暂无数据'
      // 如果弹窗已经打开，刷新数据采集时间和所有图表数据
      if (this.smartIOTHistoryDialog) {
        this.getWeatherAreaData(this.areaId) // 更新数据采集时间
        this.initAllCharts()
      }
    })
  },
  methods:{
    // 弹窗打开时调用
    handleOpen(){
      this.$nextTick(()=>{
        // 移除重复的智慧物联数据请求，直接使用传入的数据
        this.getWeatherAreaData(this.areaId) // 获取数据采集时间
        this.initAllCharts()
      })
    },

    // 关闭弹窗并重置状态
    closeDialog(){
      this.smartIOTHistoryDialog = false
      this.resetTimeSelections()
    },

    // 弹窗关闭时调用（监听el-dialog的close事件）
    handleClose(){
      this.resetTimeSelections()
    },

    // 重置所有时间选择状态为默认值
    resetTimeSelections(){
      this.airTHTimeActive = 0
      this.co2TimeActive = 0
      this.illuminanceActive = 0
      this.soilTHTimeActive = 1
      this.conductivityActive = 1
    },

    // 根据区域ID获取数据采集时间
    async getWeatherAreaData(areaId){
      if (!areaId) return
      try {
        const res = await WeatherService.weatherAreaData(areaId)
        this.collectDateTime = res.collectTime 
      } catch (err) {
        console.error('获取数据采集时间失败:', err)
        this.collectDateTime = '' // 失败时清空时间显示
      }
    },

    // 初始化所有图表
    initAllCharts() {
      if (!this.areaId) {
        console.error('缺少areaId参数')
        return
      }
      this.getAirTHChart()
      this.getCO2Chart()
      this.getIlluminanceChart()
      this.getSoilTHChart()
      this.getConductivityChart()
    },
    
    // 空气温湿度点击
    airTHTimeCheck(num){
      this.airTHTimeActive = num
      this.getAirTHChart()
    },
    
    // 获取空气温湿度图表 - 完全参照meteorologyEMDialog
    getAirTHChart(){
      if(this.airTHTimeActive == 0){
        // 本天数据
        WeatherService.temperatureChartDay(this.areaId)
        .then(res=>{
          chart.THLineToday(res).then(data=>{
            this.airTHLineOption = data
          })
        })
        .catch(err => {
          console.error('获取空气温湿度本天数据失败:', err)
          this.airTHLineOption = { show: false, option: {} }
        })
      } else {
        // 近七天或近30天数据
        let po = {
          areaId: this.areaId,
          showType: this.airTHTimeActive
        }
        WeatherService.temperatureChart(po)
        .then(res=>{
          if(res.weatherChartVoList==null||res.weatherChartVoList.length==0){
            this.airTHLineOption = {
              show: false,
              option: {},
            } 
          } else {
            chart.THLine(res.weatherChartVoList).then(data=>{
              this.airTHLineOption = data
            })
          }
        })
        .catch(err => {
          console.error('获取空气温湿度数据失败:', err)
          this.airTHLineOption = { show: false, option: {} }
        })
      }
    },
    
    // 二氧化碳点击
    co2TimeCheck(num){
      this.co2TimeActive = num
      this.getCO2Chart()
    },
    
    // 获取二氧化碳图表 - 从温湿度接口获取CO2数据
    getCO2Chart(){
      if(this.co2TimeActive == 0){
        // 本天数据
        WeatherService.temperatureChartDay(this.areaId)
        .then(res=>{
          const co2Data = res ? res.map(item => ({
            time: item.showTime,
            value: item.carbonDioxide || 0
          })) : []
          chart.CO2Line(co2Data).then(data=>{
            this.co2LineOption = data
          })
        })
        .catch(err => {
          console.error('获取CO2本天数据失败:', err)
          this.co2LineOption = { show: false, option: {} }
        })
      } else {
        // 近七天或近30天数据
        let po = {
          areaId: this.areaId,
          showType: this.co2TimeActive
        }
        WeatherService.temperatureChart(po)
        .then(res=>{
          if(res.weatherChartVoList==null||res.weatherChartVoList.length==0){
            this.co2LineOption = {
              show: false,
              option: {},
            } 
          } else {
            const co2Data = res.weatherChartVoList.map(item => ({
              time: item.showTime,
              value: item.carbonDioxideAvg || 0
            }))
            chart.CO2Line(co2Data).then(data=>{
              this.co2LineOption = data
            })
          }
        })
        .catch(err => {
          console.error('获取CO2数据失败:', err)
          this.co2LineOption = { show: false, option: {} }
        })
      }
    },
    
    // 光照度点击
    illuminanceCheck(num){
      this.illuminanceActive = num
      this.getIlluminanceChart()
    },
    
    // 获取光照度图表 - 完全参照meteorologyEMDialog
    getIlluminanceChart(){
      let po = {
        areaId: this.areaId,
        showType: this.illuminanceActive
      }
      WeatherService.illuminanceChart(po)
      .then(res=>{
        if(res.illuminanceInfo==null||res.illuminanceInfo.length==0){
          this.illuminanceLineOption = {
            show: false,
            option: {},
          } 
        } else {
          chart.IlluminanceLine(res.illuminanceInfo ? res.illuminanceInfo : []).then(data=>{
            this.illuminanceLineOption = data
          })
        }
      })
      .catch(err => {
        console.error('获取光照度数据失败:', err)
        this.illuminanceLineOption = { show: false, option: {} }
      })
    },
    
    // 土壤温湿度点击
    soilTHTimeCheck(num){
      this.soilTHTimeActive = num
      this.getSoilTHChart()
    },
    
    // 获取土壤温湿度图表
    getSoilTHChart(){
      let now = gettime.currentdate()
      let start = this.soilTHTimeActive === 1 ? gettime.getSevenAaysAgo() : gettime.getPreMonthDay(now)
      
      let po = {
        areaId: this.areaId,
        layer: 0, // 默认使用第一层土壤
        startTime: start,
        endTime: now
      }
      
      SoilService.listChart(po)
      .then(res=>{
        if(res==null||res.length==0){
          this.soilTHLineOption = {
            show: false,
            option: {},
          }
        } else {
          let name=[],soilT=[],soilH=[]
          res.forEach(e => {
            name.push(e.showTime)
            soilT.push(e.value.temperatureAvg)
            soilH.push(e.value.humidityAvg)
          })
          chart.soilTHLine(name, soilT, soilH).then(data=>{
            this.soilTHLineOption = data
          })
        }
      })
      .catch(err => {
        console.error('获取土壤温湿度数据失败:', err)
        this.soilTHLineOption = { show: false, option: {} }
      })
    },
    
    // 电导率点击
    conductivityCheck(num){
      this.conductivityActive = num
      this.getConductivityChart()
    },
    
    // 获取电导率图表 - 完全参照soilEMDialog的方式
    getConductivityChart(){
      let now = gettime.currentdate()
      let start = this.conductivityActive === 1 ? gettime.getSevenAaysAgo() : gettime.getPreMonthDay(now)
      
      let po = {
        areaId: this.areaId,
        layer: 0, // 默认使用第一层土壤
        startTime: start,
        endTime: now
      }
      
      SoilService.listChart(po)
      .then(res=>{
        if(res==null||res.length==0){
          this.conductivityLineOption = {
            show: false,
            option: {},
          }
        } else {
          let name=[],conductivity=[]
          res.forEach(e => {
            name.push(e.showTime)
            conductivity.push(e.value.conductivityAvg)
          })
          chart.conductivityLine(name, conductivity).then(data=>{
            this.conductivityLineOption = data
          })
        }
      })
      .catch(err => {
        console.error('获取电导率数据失败:', err)
        this.conductivityLineOption = { show: false, option: {} }
      })
    },
    


    // 打开历史记录弹窗
    openHistoryDialog(){
      this.zEmit('meteorologyOfHistoryDialogOpen',this.areaId);
    },

    // 处理汇报更新下拉菜单选择
    handleReportCommand(command) {
      if (command === 'meteorology') {
        // 选择了气象数据汇报
        this.zEmit('meteorologyReportUpdateDialogOpen', this.areaId);
      } else if (command === 'soil') {
        // 选择了土壤数据汇报
        this.zEmit('soilReportUpdateDialogOpen', this.areaId);
      }
    },


  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>

<style lang="less">
@import '../../assets/css/Dialog/SmartIOTHistoryDialog.less';
</style>
